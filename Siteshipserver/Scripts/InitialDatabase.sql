-- SiteShip.ai Database Schema
-- PostgreSQL Database Setup Script

-- Create database (run this separately if needed)
-- CREATE DATABASE siteship;

-- Connect to the siteship database before running the rest

-- Create Users table
CREATE TABLE IF NOT EXISTS "Users" (
    "Id" SERIAL PRIMARY KEY,
    "Username" VARCHAR(100) NOT NULL,
    "Email" VARCHAR(255) NOT NULL,
    "PasswordHash" TEXT NOT NULL,
    "FirstName" VARCHAR(100),
    "LastName" VARCHAR(100),
    "SubscriptionTier" INTEGER NOT NULL DEFAULT 0,
    "CreatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "IsActive" BOOLEAN NOT NULL DEFAULT TRUE,
    "LastLoginAt" TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT "UQ_Users_Email" UNIQUE ("Email"),
    CONSTRAINT "UQ_Users_Username" UNIQUE ("Username")
);

-- Create Projects table
CREATE TABLE IF NOT EXISTS "Projects" (
    "Id" SERIAL PRIMARY KEY,
    "Name" VARCHAR(200) NOT NULL,
    "Description" VARCHAR(1000),
    "Type" INTEGER NOT NULL,
    "ContainerStatus" INTEGER NOT NULL DEFAULT 0,
    "ContainerId" VARCHAR(100),
    "ContainerPort" VARCHAR(50),
    "IsPublic" BOOLEAN NOT NULL DEFAULT FALSE,
    "CreatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "LastDeployedAt" TIMESTAMP WITH TIME ZONE,
    "UserId" INTEGER NOT NULL,
    
    CONSTRAINT "FK_Projects_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("Id") ON DELETE CASCADE,
    CONSTRAINT "UQ_Projects_UserId_Name" UNIQUE ("UserId", "Name")
);

-- Create ProjectFiles table
CREATE TABLE IF NOT EXISTS "ProjectFiles" (
    "Id" SERIAL PRIMARY KEY,
    "FilePath" VARCHAR(500) NOT NULL,
    "FileName" VARCHAR(255) NOT NULL,
    "Content" TEXT NOT NULL,
    "MimeType" VARCHAR(100),
    "FileSize" BIGINT NOT NULL,
    "CreatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "IsDeleted" BOOLEAN NOT NULL DEFAULT FALSE,
    "ProjectId" INTEGER NOT NULL,
    
    CONSTRAINT "FK_ProjectFiles_Projects_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES "Projects" ("Id") ON DELETE CASCADE,
    CONSTRAINT "UQ_ProjectFiles_ProjectId_FilePath" UNIQUE ("ProjectId", "FilePath")
);

-- Create ScanReports table
CREATE TABLE IF NOT EXISTS "ScanReports" (
    "Id" SERIAL PRIMARY KEY,
    "Type" INTEGER NOT NULL,
    "Status" INTEGER NOT NULL DEFAULT 0,
    "Results" TEXT NOT NULL,
    "Score" INTEGER NOT NULL DEFAULT 0,
    "Summary" VARCHAR(1000),
    "CreatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "CompletedAt" TIMESTAMP WITH TIME ZONE,
    "ErrorMessage" VARCHAR(1000),
    "ProjectId" INTEGER NOT NULL,
    "UserId" INTEGER NOT NULL,
    
    CONSTRAINT "FK_ScanReports_Projects_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES "Projects" ("Id") ON DELETE CASCADE,
    CONSTRAINT "FK_ScanReports_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users" ("Id") ON DELETE RESTRICT
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "IX_Projects_UserId" ON "Projects" ("UserId");
CREATE INDEX IF NOT EXISTS "IX_Projects_IsPublic" ON "Projects" ("IsPublic");
CREATE INDEX IF NOT EXISTS "IX_Projects_CreatedAt" ON "Projects" ("CreatedAt");

CREATE INDEX IF NOT EXISTS "IX_ProjectFiles_ProjectId" ON "ProjectFiles" ("ProjectId");
CREATE INDEX IF NOT EXISTS "IX_ProjectFiles_IsDeleted" ON "ProjectFiles" ("IsDeleted");

CREATE INDEX IF NOT EXISTS "IX_ScanReports_ProjectId" ON "ScanReports" ("ProjectId");
CREATE INDEX IF NOT EXISTS "IX_ScanReports_UserId" ON "ScanReports" ("UserId");
CREATE INDEX IF NOT EXISTS "IX_ScanReports_Type" ON "ScanReports" ("Type");
CREATE INDEX IF NOT EXISTS "IX_ScanReports_Status" ON "ScanReports" ("Status");
CREATE INDEX IF NOT EXISTS "IX_ScanReports_CreatedAt" ON "ScanReports" ("CreatedAt");

-- Create Hangfire schema and tables
CREATE SCHEMA IF NOT EXISTS hangfire;

-- Hangfire tables (basic structure - Hangfire will create the full schema)
CREATE TABLE IF NOT EXISTS hangfire.job (
    id BIGSERIAL PRIMARY KEY,
    stateid INTEGER,
    statename VARCHAR(20),
    invocationdata TEXT NOT NULL,
    arguments TEXT NOT NULL,
    createdat TIMESTAMP WITH TIME ZONE NOT NULL,
    expireat TIMESTAMP WITH TIME ZONE
);

CREATE TABLE IF NOT EXISTS hangfire.state (
    id BIGSERIAL PRIMARY KEY,
    jobid BIGINT NOT NULL,
    name VARCHAR(20) NOT NULL,
    reason VARCHAR(100),
    createdat TIMESTAMP WITH TIME ZONE NOT NULL,
    data TEXT
);

-- Insert sample data (optional)
-- Sample user (password is 'password123' hashed with BCrypt)
INSERT INTO "Users" ("Username", "Email", "PasswordHash", "FirstName", "LastName", "SubscriptionTier", "IsActive")
VALUES 
    ('admin', '<EMAIL>', '$2a$11$8K1p/a0dURXAm7QiK6H7iOCVjHxEv5FkqZNDoUjYuqss4eG9QDWka', 'Admin', 'User', 2, TRUE),
    ('demo', '<EMAIL>', '$2a$11$8K1p/a0dURXAm7QiK6H7iOCVjHxEv5FkqZNDoUjYuqss4eG9QDWka', 'Demo', 'User', 0, TRUE)
ON CONFLICT ("Email") DO NOTHING;

-- Sample project
INSERT INTO "Projects" ("Name", "Description", "Type", "IsPublic", "UserId")
SELECT 
    'Sample Static Website',
    'A sample static website project for demonstration',
    0, -- Static project type
    TRUE,
    u."Id"
FROM "Users" u 
WHERE u."Username" = 'demo'
ON CONFLICT ("UserId", "Name") DO NOTHING;

-- Sample project files
INSERT INTO "ProjectFiles" ("FilePath", "FileName", "Content", "MimeType", "FileSize", "ProjectId")
SELECT 
    '/index.html',
    'index.html',
    '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to SiteShip.ai</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        p { color: #666; line-height: 1.6; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Welcome to SiteShip.ai</h1>
        <p>This is a sample static website created with SiteShip.ai platform.</p>
        <p>You can edit this file and see your changes in real-time!</p>
    </div>
</body>
</html>',
    'text/html',
    LENGTH('<!DOCTYPE html>...'), -- This will be calculated
    p."Id"
FROM "Projects" p 
JOIN "Users" u ON p."UserId" = u."Id"
WHERE u."Username" = 'demo' AND p."Name" = 'Sample Static Website'
ON CONFLICT ("ProjectId", "FilePath") DO NOTHING;

-- Create a function to update the UpdatedAt timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."UpdatedAt" = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update UpdatedAt
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON "Users" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON "Projects" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_project_files_updated_at BEFORE UPDATE ON "ProjectFiles" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions (adjust as needed for your setup)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO siteship_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO siteship_user;
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA hangfire TO siteship_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA hangfire TO siteship_user;

COMMIT;
