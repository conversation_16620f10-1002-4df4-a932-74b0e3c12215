using Hangfire;
using Microsoft.EntityFrameworkCore;
using Siteshipserver.Data;
using Siteshipserver.DTOs;
using Siteshipserver.Models;
using System.Text.Json;

namespace Siteshipserver.Services;

public class ScanService : IScanService
{
    private readonly ApplicationDbContext _context;
    private readonly IDockerService _dockerService;
    private readonly ILogger<ScanService> _logger;
    private readonly IBackgroundJobClient _backgroundJobClient;

    public ScanService(
        ApplicationDbContext context, 
        IDockerService dockerService, 
        ILogger<ScanService> logger,
        IBackgroundJobClient backgroundJobClient)
    {
        _context = context;
        _dockerService = dockerService;
        _logger = logger;
        _backgroundJobClient = backgroundJobClient;
    }

    public async Task<ScanReportDto> InitiateScanAsync(int userId, ScanRequestDto requestDto)
    {
        // Verify project ownership
        var project = await _context.Projects
            .FirstOrDefaultAsync(p => p.Id == requestDto.ProjectId && p.UserId == userId);

        if (project == null)
        {
            throw new KeyNotFoundException("Project not found or access denied");
        }

        // Create scan report
        var scanReport = new ScanReport
        {
            Type = requestDto.Type,
            Status = ScanStatus.Pending,
            Results = "{}",
            ProjectId = requestDto.ProjectId,
            UserId = userId,
            CreatedAt = DateTime.UtcNow
        };

        _context.ScanReports.Add(scanReport);
        await _context.SaveChangesAsync();

        // Queue background job based on scan type
        var jobId = requestDto.Type switch
        {
            ScanType.Security => _backgroundJobClient.Enqueue(() => ProcessSecurityScanAsync(scanReport.Id)),
            ScanType.SEO => _backgroundJobClient.Enqueue(() => ProcessSeoScanAsync(scanReport.Id)),
            ScanType.Performance => _backgroundJobClient.Enqueue(() => ProcessPerformanceScanAsync(scanReport.Id)),
            ScanType.Accessibility => _backgroundJobClient.Enqueue(() => ProcessAccessibilityScanAsync(scanReport.Id)),
            _ => throw new ArgumentException("Invalid scan type")
        };

        _logger.LogInformation($"Initiated {requestDto.Type} scan {scanReport.Id} for project {requestDto.ProjectId} (Job: {jobId})");

        return MapToScanReportDto(scanReport);
    }

    public async Task<ScanReportDto> GetScanReportAsync(int reportId, int userId)
    {
        var report = await _context.ScanReports
            .Include(r => r.Project)
            .FirstOrDefaultAsync(r => r.Id == reportId && r.UserId == userId);

        if (report == null)
        {
            throw new KeyNotFoundException("Scan report not found or access denied");
        }

        return MapToScanReportDto(report);
    }

    public async Task<List<ScanReportDto>> GetProjectScanReportsAsync(int projectId, int userId)
    {
        // Verify project ownership
        var project = await _context.Projects
            .FirstOrDefaultAsync(p => p.Id == projectId && (p.UserId == userId || p.IsPublic));

        if (project == null)
        {
            throw new KeyNotFoundException("Project not found or access denied");
        }

        var reports = await _context.ScanReports
            .Where(r => r.ProjectId == projectId)
            .OrderByDescending(r => r.CreatedAt)
            .ToListAsync();

        return reports.Select(MapToScanReportDto).ToList();
    }

    public async Task<ScanResultsDto> GetProjectScanResultsAsync(int projectId, int userId)
    {
        // Verify project ownership
        var project = await _context.Projects
            .FirstOrDefaultAsync(p => p.Id == projectId && (p.UserId == userId || p.IsPublic));

        if (project == null)
        {
            throw new KeyNotFoundException("Project not found or access denied");
        }

        var reports = await _context.ScanReports
            .Where(r => r.ProjectId == projectId)
            .OrderByDescending(r => r.CreatedAt)
            .ToListAsync();

        var reportDtos = reports.Select(MapToScanReportDto).ToList();

        return new ScanResultsDto
        {
            SecurityScans = reportDtos.Where(r => r.Type == ScanType.Security).ToList(),
            SeoScans = reportDtos.Where(r => r.Type == ScanType.SEO).ToList(),
            PerformanceScans = reportDtos.Where(r => r.Type == ScanType.Performance).ToList(),
            AccessibilityScans = reportDtos.Where(r => r.Type == ScanType.Accessibility).ToList(),
            Summary = new ScanSummaryDto
            {
                TotalScans = reportDtos.Count,
                CompletedScans = reportDtos.Count(r => r.Status == ScanStatus.Completed),
                FailedScans = reportDtos.Count(r => r.Status == ScanStatus.Failed),
                PendingScans = reportDtos.Count(r => r.Status == ScanStatus.Pending || r.Status == ScanStatus.Running),
                AverageScore = reportDtos.Where(r => r.Status == ScanStatus.Completed).Any() 
                    ? reportDtos.Where(r => r.Status == ScanStatus.Completed).Average(r => r.Score) 
                    : 0,
                LastScanDate = reportDtos.Any() ? reportDtos.Max(r => r.CreatedAt) : null
            }
        };
    }

    public async Task<bool> CancelScanAsync(int reportId, int userId)
    {
        var report = await _context.ScanReports
            .FirstOrDefaultAsync(r => r.Id == reportId && r.UserId == userId);

        if (report == null)
        {
            throw new KeyNotFoundException("Scan report not found or access denied");
        }

        if (report.Status == ScanStatus.Pending || report.Status == ScanStatus.Running)
        {
            report.Status = ScanStatus.Cancelled;
            report.ErrorMessage = "Scan cancelled by user";
            await _context.SaveChangesAsync();

            _logger.LogInformation($"Cancelled scan {reportId}");
            return true;
        }

        return false;
    }

    public async Task ProcessSecurityScanAsync(int reportId)
    {
        var report = await _context.ScanReports
            .Include(r => r.Project)
            .FirstOrDefaultAsync(r => r.Id == reportId);

        if (report == null) return;

        try
        {
            report.Status = ScanStatus.Running;
            await _context.SaveChangesAsync();

            // Simulate security scanning
            await Task.Delay(5000); // Simulate processing time

            var results = new
            {
                vulnerabilities = new[]
                {
                    new { severity = "low", description = "Missing security headers", file = "index.html" },
                    new { severity = "medium", description = "Potential XSS vulnerability", file = "script.js" }
                },
                score = 75,
                recommendations = new[]
                {
                    "Add Content Security Policy headers",
                    "Sanitize user inputs",
                    "Use HTTPS for all connections"
                }
            };

            report.Results = JsonSerializer.Serialize(results);
            report.Score = 75;
            report.Summary = "Found 2 potential security issues. Overall security score: 75/100";
            report.Status = ScanStatus.Completed;
            report.CompletedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation($"Completed security scan {reportId}");
        }
        catch (Exception ex)
        {
            report.Status = ScanStatus.Failed;
            report.ErrorMessage = ex.Message;
            await _context.SaveChangesAsync();

            _logger.LogError(ex, $"Failed to process security scan {reportId}");
        }
    }

    public async Task ProcessSeoScanAsync(int reportId)
    {
        var report = await _context.ScanReports
            .Include(r => r.Project)
            .FirstOrDefaultAsync(r => r.Id == reportId);

        if (report == null) return;

        try
        {
            report.Status = ScanStatus.Running;
            await _context.SaveChangesAsync();

            // Simulate SEO scanning
            await Task.Delay(7000); // Simulate processing time

            var results = new
            {
                seoIssues = new[]
                {
                    new { type = "missing_meta_description", page = "/index.html", impact = "medium" },
                    new { type = "missing_alt_text", page = "/about.html", impact = "low" }
                },
                score = 82,
                recommendations = new[]
                {
                    "Add meta descriptions to all pages",
                    "Include alt text for all images",
                    "Optimize page loading speed"
                }
            };

            report.Results = JsonSerializer.Serialize(results);
            report.Score = 82;
            report.Summary = "Found 2 SEO optimization opportunities. Overall SEO score: 82/100";
            report.Status = ScanStatus.Completed;
            report.CompletedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation($"Completed SEO scan {reportId}");
        }
        catch (Exception ex)
        {
            report.Status = ScanStatus.Failed;
            report.ErrorMessage = ex.Message;
            await _context.SaveChangesAsync();

            _logger.LogError(ex, $"Failed to process SEO scan {reportId}");
        }
    }

    public async Task ProcessPerformanceScanAsync(int reportId)
    {
        var report = await _context.ScanReports
            .Include(r => r.Project)
            .FirstOrDefaultAsync(r => r.Id == reportId);

        if (report == null) return;

        try
        {
            report.Status = ScanStatus.Running;
            await _context.SaveChangesAsync();

            // Simulate performance scanning
            await Task.Delay(6000); // Simulate processing time

            var results = new
            {
                metrics = new
                {
                    firstContentfulPaint = 1.2,
                    largestContentfulPaint = 2.1,
                    cumulativeLayoutShift = 0.05,
                    firstInputDelay = 45
                },
                score = 88,
                recommendations = new[]
                {
                    "Optimize images for web",
                    "Minify CSS and JavaScript",
                    "Enable browser caching"
                }
            };

            report.Results = JsonSerializer.Serialize(results);
            report.Score = 88;
            report.Summary = "Good performance metrics. Overall performance score: 88/100";
            report.Status = ScanStatus.Completed;
            report.CompletedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation($"Completed performance scan {reportId}");
        }
        catch (Exception ex)
        {
            report.Status = ScanStatus.Failed;
            report.ErrorMessage = ex.Message;
            await _context.SaveChangesAsync();

            _logger.LogError(ex, $"Failed to process performance scan {reportId}");
        }
    }

    public async Task ProcessAccessibilityScanAsync(int reportId)
    {
        var report = await _context.ScanReports
            .Include(r => r.Project)
            .FirstOrDefaultAsync(r => r.Id == reportId);

        if (report == null) return;

        try
        {
            report.Status = ScanStatus.Running;
            await _context.SaveChangesAsync();

            // Simulate accessibility scanning
            await Task.Delay(4000); // Simulate processing time

            var results = new
            {
                accessibilityIssues = new[]
                {
                    new { type = "missing_aria_labels", count = 3, severity = "medium" },
                    new { type = "low_contrast_text", count = 1, severity = "high" }
                },
                score = 79,
                recommendations = new[]
                {
                    "Add ARIA labels to interactive elements",
                    "Increase text contrast ratios",
                    "Ensure keyboard navigation support"
                }
            };

            report.Results = JsonSerializer.Serialize(results);
            report.Score = 79;
            report.Summary = "Found 4 accessibility issues. Overall accessibility score: 79/100";
            report.Status = ScanStatus.Completed;
            report.CompletedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation($"Completed accessibility scan {reportId}");
        }
        catch (Exception ex)
        {
            report.Status = ScanStatus.Failed;
            report.ErrorMessage = ex.Message;
            await _context.SaveChangesAsync();

            _logger.LogError(ex, $"Failed to process accessibility scan {reportId}");
        }
    }

    private static ScanReportDto MapToScanReportDto(ScanReport report)
    {
        return new ScanReportDto
        {
            Id = report.Id,
            Type = report.Type,
            Status = report.Status,
            Results = report.Results,
            Score = report.Score,
            Summary = report.Summary,
            CreatedAt = report.CreatedAt,
            CompletedAt = report.CompletedAt,
            ErrorMessage = report.ErrorMessage,
            ProjectId = report.ProjectId,
            UserId = report.UserId
        };
    }
}
