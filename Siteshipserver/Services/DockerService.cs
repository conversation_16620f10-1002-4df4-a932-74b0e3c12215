using Docker.DotNet;
using Docker.DotNet.Models;
using Siteshipserver.Models;
using System.Text;

namespace Siteshipserver.Services;

public class DockerService : IDockerService
{
    private readonly DockerClient _dockerClient;
    private readonly ILogger<DockerService> _logger;
    private readonly IConfiguration _configuration;

    public DockerService(ILogger<DockerService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
        
        var dockerEndpoint = _configuration.GetConnectionString("Docker") ?? "unix:///var/run/docker.sock";
        _dockerClient = new DockerClientConfiguration(new Uri(dockerEndpoint)).CreateClient();
    }

    public async Task<string> CreateContainerAsync(Project project)
    {
        try
        {
            var containerName = $"siteship-{project.Id}-{project.Name.ToLower().Replace(" ", "-")}";
            var imageName = GetImageNameForProjectType(project.Type);
            var exposedPort = GetPortForProjectType(project.Type);

            var createParams = new CreateContainerParameters
            {
                Image = imageName,
                Name = containerName,
                ExposedPorts = new Dictionary<string, EmptyStruct>
                {
                    { $"{exposedPort}/tcp", new EmptyStruct() }
                },
                HostConfig = new HostConfig
                {
                    PortBindings = new Dictionary<string, IList<PortBinding>>
                    {
                        {
                            $"{exposedPort}/tcp",
                            new List<PortBinding>
                            {
                                new PortBinding { HostPort = "0" } // Docker will assign a random port
                            }
                        }
                    },
                    Memory = GetMemoryLimitForProjectType(project.Type),
                    CPUShares = 512 // Limit CPU usage
                },
                Env = new List<string>
                {
                    $"PROJECT_ID={project.Id}",
                    $"PROJECT_NAME={project.Name}",
                    $"PROJECT_TYPE={project.Type}"
                },
                WorkingDir = "/app",
                Labels = new Dictionary<string, string>
                {
                    { "siteship.project.id", project.Id.ToString() },
                    { "siteship.project.name", project.Name },
                    { "siteship.project.type", project.Type.ToString() },
                    { "siteship.user.id", project.UserId.ToString() }
                }
            };

            var response = await _dockerClient.Containers.CreateContainerAsync(createParams);
            
            _logger.LogInformation($"Created container {response.ID} for project {project.Id}");
            return response.ID;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to create container for project {project.Id}");
            throw;
        }
    }

    public async Task<bool> StartContainerAsync(string containerId)
    {
        try
        {
            var started = await _dockerClient.Containers.StartContainerAsync(containerId, new ContainerStartParameters());
            _logger.LogInformation($"Started container {containerId}");
            return started;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to start container {containerId}");
            return false;
        }
    }

    public async Task<bool> StopContainerAsync(string containerId)
    {
        try
        {
            var stopped = await _dockerClient.Containers.StopContainerAsync(containerId, new ContainerStopParameters
            {
                WaitBeforeKillSeconds = 30
            });
            _logger.LogInformation($"Stopped container {containerId}");
            return stopped;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to stop container {containerId}");
            return false;
        }
    }

    public async Task<bool> RestartContainerAsync(string containerId)
    {
        try
        {
            await _dockerClient.Containers.RestartContainerAsync(containerId, new ContainerRestartParameters
            {
                WaitBeforeKillSeconds = 30
            });
            _logger.LogInformation($"Restarted container {containerId}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to restart container {containerId}");
            return false;
        }
    }

    public async Task<bool> DeleteContainerAsync(string containerId)
    {
        try
        {
            await _dockerClient.Containers.RemoveContainerAsync(containerId, new ContainerRemoveParameters
            {
                Force = true,
                RemoveVolumes = true
            });
            _logger.LogInformation($"Deleted container {containerId}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to delete container {containerId}");
            return false;
        }
    }

    public async Task<Models.ContainerStatus> GetContainerStatusAsync(string containerId)
    {
        try
        {
            var container = await _dockerClient.Containers.InspectContainerAsync(containerId);
            
            return container.State.Status.ToLower() switch
            {
                "running" => Models.ContainerStatus.Running,
                "exited" => Models.ContainerStatus.Stopped,
                "created" => Models.ContainerStatus.Stopped,
                "restarting" => Models.ContainerStatus.Starting,
                "removing" => Models.ContainerStatus.Stopping,
                "paused" => Models.ContainerStatus.Stopped,
                "dead" => Models.ContainerStatus.Error,
                _ => Models.ContainerStatus.Error
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to get status for container {containerId}");
            return Models.ContainerStatus.Error;
        }
    }

    public async Task<string?> GetContainerLogsAsync(string containerId, int lines = 100)
    {
        try
        {
            var logsParams = new ContainerLogsParameters
            {
                ShowStdout = true,
                ShowStderr = true,
                Tail = lines.ToString()
            };

            using var logsStream = await _dockerClient.Containers.GetContainerLogsAsync(containerId, logsParams);
            using var reader = new StreamReader(logsStream);
            return await reader.ReadToEndAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to get logs for container {containerId}");
            return null;
        }
    }

    public async Task<bool> DeployProjectAsync(Project project)
    {
        try
        {
            // This would involve building the project files into the container
            // For now, we'll just restart the container
            if (!string.IsNullOrEmpty(project.ContainerId))
            {
                return await RestartContainerAsync(project.ContainerId);
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to deploy project {project.Id}");
            return false;
        }
    }

    public async Task<string> GetContainerUrlAsync(string containerId, string? port = null)
    {
        try
        {
            var container = await _dockerClient.Containers.InspectContainerAsync(containerId);
            var hostPort = container.NetworkSettings.Ports?.FirstOrDefault().Value?.FirstOrDefault()?.HostPort;
            
            if (!string.IsNullOrEmpty(hostPort))
            {
                var baseUrl = _configuration["Docker:BaseUrl"] ?? "http://localhost";
                return $"{baseUrl}:{hostPort}";
            }
            
            return string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to get URL for container {containerId}");
            return string.Empty;
        }
    }

    public async Task<bool> UpdateContainerFilesAsync(string containerId, Dictionary<string, string> files)
    {
        try
        {
            // This would involve copying files into the running container
            // Implementation would depend on the specific container setup
            _logger.LogInformation($"Updating files in container {containerId}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to update files in container {containerId}");
            return false;
        }
    }

    private static string GetImageNameForProjectType(ProjectType type)
    {
        return type switch
        {
            ProjectType.Static => "nginx:alpine",
            ProjectType.React => "node:18-alpine",
            ProjectType.NodeJs => "node:18-alpine",
            ProjectType.Python => "python:3.11-alpine",
            ProjectType.Vue => "node:18-alpine",
            ProjectType.Angular => "node:18-alpine",
            _ => "nginx:alpine"
        };
    }

    private static string GetPortForProjectType(ProjectType type)
    {
        return type switch
        {
            ProjectType.Static => "80",
            ProjectType.React => "3000",
            ProjectType.NodeJs => "3000",
            ProjectType.Python => "8000",
            ProjectType.Vue => "8080",
            ProjectType.Angular => "4200",
            _ => "80"
        };
    }

    private static long GetMemoryLimitForProjectType(ProjectType type)
    {
        return type switch
        {
            ProjectType.Static => 128 * 1024 * 1024, // 128MB
            ProjectType.React => 512 * 1024 * 1024,  // 512MB
            ProjectType.NodeJs => 512 * 1024 * 1024, // 512MB
            ProjectType.Python => 256 * 1024 * 1024, // 256MB
            ProjectType.Vue => 512 * 1024 * 1024,    // 512MB
            ProjectType.Angular => 512 * 1024 * 1024, // 512MB
            _ => 256 * 1024 * 1024 // 256MB default
        };
    }

    public void Dispose()
    {
        _dockerClient?.Dispose();
    }
}
