using Microsoft.EntityFrameworkCore;
using Siteshipserver.Data;
using Siteshipserver.DTOs;
using Siteshipserver.Models;
using System.Text;

namespace Siteshipserver.Services;

public class FileService : IFileService
{
    private readonly ApplicationDbContext _context;
    private readonly IDockerService _dockerService;
    private readonly ILogger<FileService> _logger;

    public FileService(ApplicationDbContext context, IDockerService dockerService, ILogger<FileService> logger)
    {
        _context = context;
        _dockerService = dockerService;
        _logger = logger;
    }

    public async Task<ProjectFileDto> CreateFileAsync(int projectId, int userId, CreateFileDto createDto)
    {
        // Verify project ownership
        var project = await _context.Projects
            .FirstOrDefaultAsync(p => p.Id == projectId && p.UserId == userId);

        if (project == null)
        {
            throw new KeyNotFoundException("Project not found or access denied");
        }

        // Check if file already exists
        if (await _context.ProjectFiles.AnyAsync(f => f.ProjectId == projectId && f.FilePath == createDto.FilePath && !f.IsDeleted))
        {
            throw new InvalidOperationException("A file with this path already exists");
        }

        var file = new ProjectFile
        {
            ProjectId = projectId,
            FilePath = createDto.FilePath,
            FileName = createDto.FileName,
            Content = createDto.Content,
            MimeType = GetMimeType(createDto.FileName),
            FileSize = Encoding.UTF8.GetByteCount(createDto.Content),
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        _context.ProjectFiles.Add(file);
        await _context.SaveChangesAsync();

        _logger.LogInformation($"Created file {file.Id} for project {projectId}");

        return MapToProjectFileDto(file);
    }

    public async Task<ProjectFileDto> GetFileByIdAsync(int fileId, int userId)
    {
        var file = await _context.ProjectFiles
            .Include(f => f.Project)
            .FirstOrDefaultAsync(f => f.Id == fileId && f.Project.UserId == userId && !f.IsDeleted);

        if (file == null)
        {
            throw new KeyNotFoundException("File not found or access denied");
        }

        return MapToProjectFileDto(file);
    }

    public async Task<List<ProjectFileDto>> GetProjectFilesAsync(int projectId, int userId)
    {
        // Verify project ownership
        var project = await _context.Projects
            .FirstOrDefaultAsync(p => p.Id == projectId && (p.UserId == userId || p.IsPublic));

        if (project == null)
        {
            throw new KeyNotFoundException("Project not found or access denied");
        }

        var files = await _context.ProjectFiles
            .Where(f => f.ProjectId == projectId && !f.IsDeleted)
            .OrderBy(f => f.FilePath)
            .ToListAsync();

        return files.Select(MapToProjectFileDto).ToList();
    }

    public async Task<ProjectFileDto> UpdateFileAsync(int fileId, int userId, UpdateFileDto updateDto)
    {
        var file = await _context.ProjectFiles
            .Include(f => f.Project)
            .FirstOrDefaultAsync(f => f.Id == fileId && f.Project.UserId == userId && !f.IsDeleted);

        if (file == null)
        {
            throw new KeyNotFoundException("File not found or access denied");
        }

        if (!string.IsNullOrEmpty(updateDto.Content))
        {
            file.Content = updateDto.Content;
            file.FileSize = Encoding.UTF8.GetByteCount(updateDto.Content);
        }

        if (!string.IsNullOrEmpty(updateDto.FilePath))
        {
            // Check if new path conflicts with existing files
            if (await _context.ProjectFiles.AnyAsync(f => f.ProjectId == file.ProjectId && f.FilePath == updateDto.FilePath && f.Id != fileId && !f.IsDeleted))
            {
                throw new InvalidOperationException("A file with this path already exists");
            }
            file.FilePath = updateDto.FilePath;
        }

        if (!string.IsNullOrEmpty(updateDto.FileName))
        {
            file.FileName = updateDto.FileName;
            file.MimeType = GetMimeType(updateDto.FileName);
        }

        file.UpdatedAt = DateTime.UtcNow;
        await _context.SaveChangesAsync();

        _logger.LogInformation($"Updated file {fileId}");

        return MapToProjectFileDto(file);
    }

    public async Task<bool> DeleteFileAsync(int fileId, int userId)
    {
        var file = await _context.ProjectFiles
            .Include(f => f.Project)
            .FirstOrDefaultAsync(f => f.Id == fileId && f.Project.UserId == userId && !f.IsDeleted);

        if (file == null)
        {
            throw new KeyNotFoundException("File not found or access denied");
        }

        file.IsDeleted = true;
        file.UpdatedAt = DateTime.UtcNow;
        await _context.SaveChangesAsync();

        _logger.LogInformation($"Deleted file {fileId}");

        return true;
    }

    public async Task<ProjectFileDto> UploadFileAsync(int projectId, int userId, FileUploadDto uploadDto)
    {
        // Verify project ownership
        var project = await _context.Projects
            .FirstOrDefaultAsync(p => p.Id == projectId && p.UserId == userId);

        if (project == null)
        {
            throw new KeyNotFoundException("Project not found or access denied");
        }

        // Read file content
        using var reader = new StreamReader(uploadDto.File.OpenReadStream());
        var content = await reader.ReadToEndAsync();

        var createDto = new CreateFileDto
        {
            FilePath = uploadDto.FilePath,
            FileName = uploadDto.File.FileName,
            Content = content
        };

        return await CreateFileAsync(projectId, userId, createDto);
    }

    public async Task<bool> BulkFileOperationAsync(int projectId, int userId, BulkFileOperationDto operationDto)
    {
        // Verify project ownership
        var project = await _context.Projects
            .FirstOrDefaultAsync(p => p.Id == projectId && p.UserId == userId);

        if (project == null)
        {
            throw new KeyNotFoundException("Project not found or access denied");
        }

        var files = await _context.ProjectFiles
            .Where(f => operationDto.FileIds.Contains(f.Id) && f.ProjectId == projectId)
            .ToListAsync();

        switch (operationDto.Operation)
        {
            case FileOperation.Delete:
                foreach (var file in files)
                {
                    file.IsDeleted = true;
                    file.UpdatedAt = DateTime.UtcNow;
                }
                break;
            case FileOperation.Restore:
                foreach (var file in files)
                {
                    file.IsDeleted = false;
                    file.UpdatedAt = DateTime.UtcNow;
                }
                break;
        }

        await _context.SaveChangesAsync();

        _logger.LogInformation($"Performed bulk operation {operationDto.Operation} on {files.Count} files in project {projectId}");

        return true;
    }

    public async Task<byte[]> DownloadFileAsync(int fileId, int userId)
    {
        var file = await _context.ProjectFiles
            .Include(f => f.Project)
            .FirstOrDefaultAsync(f => f.Id == fileId && f.Project.UserId == userId && !f.IsDeleted);

        if (file == null)
        {
            throw new KeyNotFoundException("File not found or access denied");
        }

        return Encoding.UTF8.GetBytes(file.Content);
    }

    public async Task<bool> SyncFilesToContainerAsync(int projectId, int userId)
    {
        // Verify project ownership
        var project = await _context.Projects
            .FirstOrDefaultAsync(p => p.Id == projectId && p.UserId == userId);

        if (project == null)
        {
            throw new KeyNotFoundException("Project not found or access denied");
        }

        if (string.IsNullOrEmpty(project.ContainerId))
        {
            throw new InvalidOperationException("Project does not have an active container");
        }

        var files = await _context.ProjectFiles
            .Where(f => f.ProjectId == projectId && !f.IsDeleted)
            .ToListAsync();

        var fileDict = files.ToDictionary(f => f.FilePath, f => f.Content);

        var success = await _dockerService.UpdateContainerFilesAsync(project.ContainerId, fileDict);

        if (success)
        {
            _logger.LogInformation($"Synced {files.Count} files to container for project {projectId}");
        }

        return success;
    }

    private static ProjectFileDto MapToProjectFileDto(ProjectFile file)
    {
        return new ProjectFileDto
        {
            Id = file.Id,
            FilePath = file.FilePath,
            FileName = file.FileName,
            Content = file.Content,
            MimeType = file.MimeType,
            FileSize = file.FileSize,
            CreatedAt = file.CreatedAt,
            UpdatedAt = file.UpdatedAt,
            ProjectId = file.ProjectId
        };
    }

    private static string GetMimeType(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        
        return extension switch
        {
            ".html" => "text/html",
            ".css" => "text/css",
            ".js" => "application/javascript",
            ".json" => "application/json",
            ".xml" => "application/xml",
            ".txt" => "text/plain",
            ".md" => "text/markdown",
            ".py" => "text/x-python",
            ".java" => "text/x-java-source",
            ".cs" => "text/x-csharp",
            ".cpp" => "text/x-c++src",
            ".c" => "text/x-csrc",
            ".php" => "text/x-php",
            ".rb" => "text/x-ruby",
            ".go" => "text/x-go",
            ".rs" => "text/x-rust",
            ".ts" => "application/typescript",
            ".tsx" => "text/typescript-jsx",
            ".jsx" => "text/jsx",
            ".vue" => "text/x-vue",
            ".yml" or ".yaml" => "text/yaml",
            ".dockerfile" => "text/x-dockerfile",
            ".sh" => "text/x-shellscript",
            ".bat" => "text/x-msdos-batch",
            ".ps1" => "text/x-powershell",
            _ => "text/plain"
        };
    }
}
