using Siteshipserver.DTOs;
using Siteshipserver.Models;

namespace Siteshipserver.Services;

public interface IScanService
{
    Task<ScanReportDto> InitiateScanAsync(int userId, ScanRequestDto requestDto);
    Task<ScanReportDto> GetScanReportAsync(int reportId, int userId);
    Task<List<ScanReportDto>> GetProjectScanReportsAsync(int projectId, int userId);
    Task<ScanResultsDto> GetProjectScanResultsAsync(int projectId, int userId);
    Task<bool> CancelScanAsync(int reportId, int userId);
    
    // Background job methods
    Task ProcessSecurityScanAsync(int reportId);
    Task ProcessSeoScanAsync(int reportId);
    Task ProcessPerformanceScanAsync(int reportId);
    Task ProcessAccessibilityScanAsync(int reportId);
}
