using Siteshipserver.DTOs;

namespace Siteshipserver.Services;

public interface IProjectService
{
    Task<ProjectResponseDto> CreateProjectAsync(int userId, CreateProjectDto createDto);
    Task<ProjectResponseDto> GetProjectByIdAsync(int projectId, int userId);
    Task<ProjectDetailDto> GetProjectDetailAsync(int projectId, int userId);
    Task<List<ProjectResponseDto>> GetUserProjectsAsync(int userId);
    Task<ProjectResponseDto> UpdateProjectAsync(int projectId, int userId, UpdateProjectDto updateDto);
    Task<bool> DeleteProjectAsync(int projectId, int userId);
    Task<ProjectResponseDto> PerformContainerActionAsync(int projectId, int userId, ContainerActionDto actionDto);
    Task<List<ProjectResponseDto>> GetPublicProjectsAsync(int page = 1, int pageSize = 20);
}
