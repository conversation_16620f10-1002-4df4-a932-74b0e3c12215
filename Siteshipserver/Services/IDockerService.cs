using Siteshipserver.Models;

namespace Siteshipserver.Services;

public interface IDockerService
{
    Task<string> CreateContainerAsync(Project project);
    Task<bool> StartContainerAsync(string containerId);
    Task<bool> StopContainerAsync(string containerId);
    Task<bool> RestartContainerAsync(string containerId);
    Task<bool> DeleteContainerAsync(string containerId);
    Task<Models.ContainerStatus> GetContainerStatusAsync(string containerId);
    Task<string?> GetContainerLogsAsync(string containerId, int lines = 100);
    Task<bool> DeployProjectAsync(Project project);
    Task<string> GetContainerUrlAsync(string containerId, string? port = null);
    Task<bool> UpdateContainerFilesAsync(string containerId, Dictionary<string, string> files);
}
