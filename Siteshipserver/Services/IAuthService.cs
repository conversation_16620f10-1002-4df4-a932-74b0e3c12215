using Siteshipserver.DTOs;
using Siteshipserver.Models;

namespace Siteshipserver.Services;

public interface IAuthService
{
    Task<TokenDto> RegisterAsync(RegisterDto registerDto);
    Task<TokenDto> LoginAsync(LoginDto loginDto);
    Task<UserResponseDto> GetUserByIdAsync(int userId);
    Task<UserResponseDto> UpdateUserAsync(int userId, UpdateUserDto updateDto);
    Task<bool> ChangePasswordAsync(int userId, ChangePasswordDto changePasswordDto);
    string GenerateJwtToken(User user);
    Task<bool> ValidateTokenAsync(string token);
}
