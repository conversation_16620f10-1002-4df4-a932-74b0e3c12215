using Siteshipserver.DTOs;

namespace Siteshipserver.Services;

public interface IFileService
{
    Task<ProjectFileDto> CreateFileAsync(int projectId, int userId, CreateFileDto createDto);
    Task<ProjectFileDto> GetFileByIdAsync(int fileId, int userId);
    Task<List<ProjectFileDto>> GetProjectFilesAsync(int projectId, int userId);
    Task<ProjectFileDto> UpdateFileAsync(int fileId, int userId, UpdateFileDto updateDto);
    Task<bool> DeleteFileAsync(int fileId, int userId);
    Task<ProjectFileDto> UploadFileAsync(int projectId, int userId, FileUploadDto uploadDto);
    Task<bool> BulkFileOperationAsync(int projectId, int userId, BulkFileOperationDto operationDto);
    Task<byte[]> DownloadFileAsync(int fileId, int userId);
    Task<bool> SyncFilesToContainerAsync(int projectId, int userId);
}
