using Microsoft.EntityFrameworkCore;
using Siteshipserver.Data;
using Siteshipserver.DTOs;
using Siteshipserver.Models;

namespace Siteshipserver.Services;

public class ProjectService : IProjectService
{
    private readonly ApplicationDbContext _context;
    private readonly IDockerService _dockerService;
    private readonly ILogger<ProjectService> _logger;

    public ProjectService(ApplicationDbContext context, IDockerService dockerService, ILogger<ProjectService> logger)
    {
        _context = context;
        _dockerService = dockerService;
        _logger = logger;
    }

    public async Task<ProjectResponseDto> CreateProjectAsync(int userId, CreateProjectDto createDto)
    {
        // Check if project name already exists for user
        if (await _context.Projects.AnyAsync(p => p.UserId == userId && p.Name == createDto.Name))
        {
            throw new InvalidOperationException("A project with this name already exists");
        }

        var project = new Project
        {
            Name = createDto.Name,
            Description = createDto.Description,
            Type = createDto.Type,
            IsPublic = createDto.IsPublic,
            UserId = userId,
            ContainerStatus = ContainerStatus.Stopped,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        _context.Projects.Add(project);
        await _context.SaveChangesAsync();

        _logger.LogInformation($"Created project {project.Id} for user {userId}");

        return MapToProjectResponseDto(project);
    }

    public async Task<ProjectResponseDto> GetProjectByIdAsync(int projectId, int userId)
    {
        var project = await _context.Projects
            .FirstOrDefaultAsync(p => p.Id == projectId && (p.UserId == userId || p.IsPublic));

        if (project == null)
        {
            throw new KeyNotFoundException("Project not found or access denied");
        }

        var dto = MapToProjectResponseDto(project);
        
        // Get container URL if container is running
        if (!string.IsNullOrEmpty(project.ContainerId))
        {
            dto.ContainerUrl = await _dockerService.GetContainerUrlAsync(project.ContainerId, project.ContainerPort);
        }

        return dto;
    }

    public async Task<ProjectDetailDto> GetProjectDetailAsync(int projectId, int userId)
    {
        var project = await _context.Projects
            .Include(p => p.Files.Where(f => !f.IsDeleted))
            .Include(p => p.ScanReports)
            .FirstOrDefaultAsync(p => p.Id == projectId && (p.UserId == userId || p.IsPublic));

        if (project == null)
        {
            throw new KeyNotFoundException("Project not found or access denied");
        }

        var dto = new ProjectDetailDto
        {
            Id = project.Id,
            Name = project.Name,
            Description = project.Description,
            Type = project.Type,
            ContainerStatus = project.ContainerStatus,
            ContainerId = project.ContainerId,
            ContainerPort = project.ContainerPort,
            IsPublic = project.IsPublic,
            CreatedAt = project.CreatedAt,
            UpdatedAt = project.UpdatedAt,
            LastDeployedAt = project.LastDeployedAt,
            UserId = project.UserId,
            Files = project.Files.Select(MapToProjectFileDto).ToList(),
            ScanReports = project.ScanReports.Select(MapToScanReportDto).ToList()
        };

        // Get container URL if container is running
        if (!string.IsNullOrEmpty(project.ContainerId))
        {
            dto.ContainerUrl = await _dockerService.GetContainerUrlAsync(project.ContainerId, project.ContainerPort);
        }

        return dto;
    }

    public async Task<List<ProjectResponseDto>> GetUserProjectsAsync(int userId)
    {
        var projects = await _context.Projects
            .Where(p => p.UserId == userId)
            .OrderByDescending(p => p.UpdatedAt)
            .ToListAsync();

        var dtos = new List<ProjectResponseDto>();
        
        foreach (var project in projects)
        {
            var dto = MapToProjectResponseDto(project);
            
            // Get container URL if container is running
            if (!string.IsNullOrEmpty(project.ContainerId))
            {
                dto.ContainerUrl = await _dockerService.GetContainerUrlAsync(project.ContainerId, project.ContainerPort);
            }
            
            dtos.Add(dto);
        }

        return dtos;
    }

    public async Task<ProjectResponseDto> UpdateProjectAsync(int projectId, int userId, UpdateProjectDto updateDto)
    {
        var project = await _context.Projects
            .FirstOrDefaultAsync(p => p.Id == projectId && p.UserId == userId);

        if (project == null)
        {
            throw new KeyNotFoundException("Project not found or access denied");
        }

        if (!string.IsNullOrEmpty(updateDto.Name))
        {
            // Check if new name conflicts with existing projects
            if (await _context.Projects.AnyAsync(p => p.UserId == userId && p.Name == updateDto.Name && p.Id != projectId))
            {
                throw new InvalidOperationException("A project with this name already exists");
            }
            project.Name = updateDto.Name;
        }

        if (updateDto.Description != null)
            project.Description = updateDto.Description;

        if (updateDto.IsPublic.HasValue)
            project.IsPublic = updateDto.IsPublic.Value;

        project.UpdatedAt = DateTime.UtcNow;
        await _context.SaveChangesAsync();

        _logger.LogInformation($"Updated project {projectId}");

        return MapToProjectResponseDto(project);
    }

    public async Task<bool> DeleteProjectAsync(int projectId, int userId)
    {
        var project = await _context.Projects
            .FirstOrDefaultAsync(p => p.Id == projectId && p.UserId == userId);

        if (project == null)
        {
            throw new KeyNotFoundException("Project not found or access denied");
        }

        // Stop and delete container if it exists
        if (!string.IsNullOrEmpty(project.ContainerId))
        {
            await _dockerService.StopContainerAsync(project.ContainerId);
            await _dockerService.DeleteContainerAsync(project.ContainerId);
        }

        _context.Projects.Remove(project);
        await _context.SaveChangesAsync();

        _logger.LogInformation($"Deleted project {projectId}");

        return true;
    }

    public async Task<ProjectResponseDto> PerformContainerActionAsync(int projectId, int userId, ContainerActionDto actionDto)
    {
        var project = await _context.Projects
            .FirstOrDefaultAsync(p => p.Id == projectId && p.UserId == userId);

        if (project == null)
        {
            throw new KeyNotFoundException("Project not found or access denied");
        }

        switch (actionDto.Action)
        {
            case ContainerAction.Start:
                await StartContainerAsync(project);
                break;
            case ContainerAction.Stop:
                await StopContainerAsync(project);
                break;
            case ContainerAction.Restart:
                await RestartContainerAsync(project);
                break;
            case ContainerAction.Deploy:
                await DeployContainerAsync(project);
                break;
        }

        await _context.SaveChangesAsync();
        
        var dto = MapToProjectResponseDto(project);
        
        // Get container URL if container is running
        if (!string.IsNullOrEmpty(project.ContainerId))
        {
            dto.ContainerUrl = await _dockerService.GetContainerUrlAsync(project.ContainerId, project.ContainerPort);
        }

        return dto;
    }

    public async Task<List<ProjectResponseDto>> GetPublicProjectsAsync(int page = 1, int pageSize = 20)
    {
        var projects = await _context.Projects
            .Where(p => p.IsPublic)
            .OrderByDescending(p => p.UpdatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return projects.Select(MapToProjectResponseDto).ToList();
    }

    private async Task StartContainerAsync(Project project)
    {
        if (string.IsNullOrEmpty(project.ContainerId))
        {
            // Create new container
            project.ContainerStatus = ContainerStatus.Starting;
            project.ContainerId = await _dockerService.CreateContainerAsync(project);
        }

        if (await _dockerService.StartContainerAsync(project.ContainerId))
        {
            project.ContainerStatus = ContainerStatus.Running;
            project.UpdatedAt = DateTime.UtcNow;
        }
        else
        {
            project.ContainerStatus = ContainerStatus.Error;
        }
    }

    private async Task StopContainerAsync(Project project)
    {
        if (!string.IsNullOrEmpty(project.ContainerId))
        {
            project.ContainerStatus = ContainerStatus.Stopping;
            
            if (await _dockerService.StopContainerAsync(project.ContainerId))
            {
                project.ContainerStatus = ContainerStatus.Stopped;
            }
            else
            {
                project.ContainerStatus = ContainerStatus.Error;
            }
            
            project.UpdatedAt = DateTime.UtcNow;
        }
    }

    private async Task RestartContainerAsync(Project project)
    {
        if (!string.IsNullOrEmpty(project.ContainerId))
        {
            project.ContainerStatus = ContainerStatus.Starting;
            
            if (await _dockerService.RestartContainerAsync(project.ContainerId))
            {
                project.ContainerStatus = ContainerStatus.Running;
            }
            else
            {
                project.ContainerStatus = ContainerStatus.Error;
            }
            
            project.UpdatedAt = DateTime.UtcNow;
        }
    }

    private async Task DeployContainerAsync(Project project)
    {
        project.ContainerStatus = ContainerStatus.Starting;
        
        if (await _dockerService.DeployProjectAsync(project))
        {
            project.ContainerStatus = ContainerStatus.Running;
            project.LastDeployedAt = DateTime.UtcNow;
        }
        else
        {
            project.ContainerStatus = ContainerStatus.Error;
        }
        
        project.UpdatedAt = DateTime.UtcNow;
    }

    private static ProjectResponseDto MapToProjectResponseDto(Project project)
    {
        return new ProjectResponseDto
        {
            Id = project.Id,
            Name = project.Name,
            Description = project.Description,
            Type = project.Type,
            ContainerStatus = project.ContainerStatus,
            ContainerId = project.ContainerId,
            ContainerPort = project.ContainerPort,
            IsPublic = project.IsPublic,
            CreatedAt = project.CreatedAt,
            UpdatedAt = project.UpdatedAt,
            LastDeployedAt = project.LastDeployedAt,
            UserId = project.UserId
        };
    }

    private static ProjectFileDto MapToProjectFileDto(ProjectFile file)
    {
        return new ProjectFileDto
        {
            Id = file.Id,
            FilePath = file.FilePath,
            FileName = file.FileName,
            Content = file.Content,
            MimeType = file.MimeType,
            FileSize = file.FileSize,
            CreatedAt = file.CreatedAt,
            UpdatedAt = file.UpdatedAt,
            ProjectId = file.ProjectId
        };
    }

    private static ScanReportDto MapToScanReportDto(ScanReport report)
    {
        return new ScanReportDto
        {
            Id = report.Id,
            Type = report.Type,
            Status = report.Status,
            Results = report.Results,
            Score = report.Score,
            Summary = report.Summary,
            CreatedAt = report.CreatedAt,
            CompletedAt = report.CompletedAt,
            ErrorMessage = report.ErrorMessage,
            ProjectId = report.ProjectId,
            UserId = report.UserId
        };
    }
}
