{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information", "Hangfire": "Information"}}, "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=siteship_dev;Username=postgres;Password=dev_password", "Docker": "unix:///var/run/docker.sock"}, "JwtSettings": {"SecretKey": "development-jwt-secret-key-for-siteship-ai-platform-minimum-32-chars"}}