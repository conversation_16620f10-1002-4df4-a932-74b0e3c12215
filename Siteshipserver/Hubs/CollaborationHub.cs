using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Siteshipserver.Data;
using System.Security.Claims;

namespace Siteshipserver.Hubs;

[Authorize]
public class CollaborationHub : Hub
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<CollaborationHub> _logger;

    public CollaborationHub(ApplicationDbContext context, ILogger<CollaborationHub> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task JoinProject(int projectId)
    {
        var userId = GetUserId();
        if (userId == null) return;

        // Verify user has access to the project
        var hasAccess = await _context.Projects
            .AnyAsync(p => p.Id == projectId && (p.UserId == userId || p.IsPublic));

        if (!hasAccess)
        {
            await Clients.Caller.SendAsync("Error", "Access denied to project");
            return;
        }

        var groupName = $"project_{projectId}";
        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
        
        // Notify other users in the project
        await Clients.OthersInGroup(groupName).SendAsync("UserJoined", new
        {
            UserId = userId,
            Username = GetUsername(),
            ProjectId = projectId,
            Timestamp = DateTime.UtcNow
        });

        _logger.LogInformation($"User {userId} joined project {projectId}");
    }

    public async Task LeaveProject(int projectId)
    {
        var userId = GetUserId();
        if (userId == null) return;

        var groupName = $"project_{projectId}";
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
        
        // Notify other users in the project
        await Clients.OthersInGroup(groupName).SendAsync("UserLeft", new
        {
            UserId = userId,
            Username = GetUsername(),
            ProjectId = projectId,
            Timestamp = DateTime.UtcNow
        });

        _logger.LogInformation($"User {userId} left project {projectId}");
    }

    public async Task SendFileChange(int projectId, int fileId, string content, int cursorPosition)
    {
        var userId = GetUserId();
        if (userId == null) return;

        // Verify user has access to the project
        var hasAccess = await _context.Projects
            .AnyAsync(p => p.Id == projectId && p.UserId == userId);

        if (!hasAccess)
        {
            await Clients.Caller.SendAsync("Error", "Access denied to project");
            return;
        }

        var groupName = $"project_{projectId}";
        
        // Broadcast file change to other users in the project
        await Clients.OthersInGroup(groupName).SendAsync("FileChanged", new
        {
            UserId = userId,
            Username = GetUsername(),
            ProjectId = projectId,
            FileId = fileId,
            Content = content,
            CursorPosition = cursorPosition,
            Timestamp = DateTime.UtcNow
        });

        _logger.LogInformation($"User {userId} made changes to file {fileId} in project {projectId}");
    }

    public async Task SendCursorPosition(int projectId, int fileId, int line, int column)
    {
        var userId = GetUserId();
        if (userId == null) return;

        var groupName = $"project_{projectId}";
        
        // Broadcast cursor position to other users in the project
        await Clients.OthersInGroup(groupName).SendAsync("CursorMoved", new
        {
            UserId = userId,
            Username = GetUsername(),
            ProjectId = projectId,
            FileId = fileId,
            Line = line,
            Column = column,
            Timestamp = DateTime.UtcNow
        });
    }

    public async Task SendChatMessage(int projectId, string message)
    {
        var userId = GetUserId();
        if (userId == null) return;

        // Verify user has access to the project
        var hasAccess = await _context.Projects
            .AnyAsync(p => p.Id == projectId && (p.UserId == userId || p.IsPublic));

        if (!hasAccess)
        {
            await Clients.Caller.SendAsync("Error", "Access denied to project");
            return;
        }

        var groupName = $"project_{projectId}";
        
        // Broadcast chat message to all users in the project
        await Clients.Group(groupName).SendAsync("ChatMessage", new
        {
            UserId = userId,
            Username = GetUsername(),
            ProjectId = projectId,
            Message = message,
            Timestamp = DateTime.UtcNow
        });

        _logger.LogInformation($"User {userId} sent chat message in project {projectId}");
    }

    public async Task NotifyContainerStatusChange(int projectId, string status)
    {
        var userId = GetUserId();
        if (userId == null) return;

        // Verify user owns the project
        var isOwner = await _context.Projects
            .AnyAsync(p => p.Id == projectId && p.UserId == userId);

        if (!isOwner)
        {
            await Clients.Caller.SendAsync("Error", "Only project owner can update container status");
            return;
        }

        var groupName = $"project_{projectId}";
        
        // Broadcast container status change to all users in the project
        await Clients.Group(groupName).SendAsync("ContainerStatusChanged", new
        {
            ProjectId = projectId,
            Status = status,
            Timestamp = DateTime.UtcNow
        });

        _logger.LogInformation($"Container status changed to {status} for project {projectId}");
    }

    public async Task NotifyScanProgress(int projectId, int scanId, string scanType, string status, int? progress = null)
    {
        var userId = GetUserId();
        if (userId == null) return;

        // Verify user has access to the project
        var hasAccess = await _context.Projects
            .AnyAsync(p => p.Id == projectId && (p.UserId == userId || p.IsPublic));

        if (!hasAccess)
        {
            await Clients.Caller.SendAsync("Error", "Access denied to project");
            return;
        }

        var groupName = $"project_{projectId}";
        
        // Broadcast scan progress to all users in the project
        await Clients.Group(groupName).SendAsync("ScanProgress", new
        {
            ProjectId = projectId,
            ScanId = scanId,
            ScanType = scanType,
            Status = status,
            Progress = progress,
            Timestamp = DateTime.UtcNow
        });

        _logger.LogInformation($"Scan {scanId} progress updated: {status} for project {projectId}");
    }

    public override async Task OnConnectedAsync()
    {
        var userId = GetUserId();
        if (userId != null)
        {
            _logger.LogInformation($"User {userId} connected to collaboration hub");
        }
        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        var userId = GetUserId();
        if (userId != null)
        {
            _logger.LogInformation($"User {userId} disconnected from collaboration hub");
        }
        await base.OnDisconnectedAsync(exception);
    }

    private int? GetUserId()
    {
        var userIdClaim = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return int.TryParse(userIdClaim, out var userId) ? userId : null;
    }

    private string? GetUsername()
    {
        return Context.User?.FindFirst(ClaimTypes.Name)?.Value;
    }
}
