# Use the official .NET 8.0 SDK image for building
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy csproj and restore dependencies
COPY ["Siteshipserver.csproj", "."]
RUN dotnet restore "Siteshipserver.csproj"

# Copy everything else and build
COPY . .
WORKDIR "/src"
RUN dotnet build "Siteshipserver.csproj" -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish "Siteshipserver.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Use the official .NET 8.0 runtime image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS final
WORKDIR /app

# Install Docker CLI (for container management)
RUN apt-get update && apt-get install -y \
    docker.io \
    && rm -rf /var/lib/apt/lists/*

# Copy the published application
COPY --from=publish /app/publish .

# Create a non-root user
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser

# Expose ports
EXPOSE 80
EXPOSE 443

ENTRYPOINT ["dotnet", "Siteshipserver.dll"]
