using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Siteshipserver.DTOs;
using Siteshipserver.Services;
using System.Security.Claims;

namespace Siteshipserver.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ScanController : ControllerBase
{
    private readonly IScanService _scanService;
    private readonly ILogger<ScanController> _logger;

    public ScanController(IScanService scanService, ILogger<ScanController> logger)
    {
        _scanService = scanService;
        _logger = logger;
    }

    /// <summary>
    /// Initiate a new scan for a project
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<ScanReportDto>> InitiateScan([FromBody] ScanRequestDto requestDto)
    {
        var userId = GetUserId();
        var scanReport = await _scanService.InitiateScanAsync(userId, requestDto);
        _logger.LogInformation($"Scan initiated: {scanReport.Id} for project {requestDto.ProjectId} by user {userId}");
        return CreatedAtAction(nameof(GetScanReport), new { id = scanReport.Id }, scanReport);
    }

    /// <summary>
    /// Get scan report by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<ScanReportDto>> GetScanReport(int id)
    {
        var userId = GetUserId();
        var scanReport = await _scanService.GetScanReportAsync(id, userId);
        return Ok(scanReport);
    }

    /// <summary>
    /// Get all scan reports for a project
    /// </summary>
    [HttpGet("project/{projectId}")]
    public async Task<ActionResult<List<ScanReportDto>>> GetProjectScanReports(int projectId)
    {
        var userId = GetUserId();
        var scanReports = await _scanService.GetProjectScanReportsAsync(projectId, userId);
        return Ok(scanReports);
    }

    /// <summary>
    /// Get comprehensive scan results for a project
    /// </summary>
    [HttpGet("project/{projectId}/results")]
    public async Task<ActionResult<ScanResultsDto>> GetProjectScanResults(int projectId)
    {
        var userId = GetUserId();
        var scanResults = await _scanService.GetProjectScanResultsAsync(projectId, userId);
        return Ok(scanResults);
    }

    /// <summary>
    /// Cancel a running scan
    /// </summary>
    [HttpPost("{id}/cancel")]
    public async Task<ActionResult> CancelScan(int id)
    {
        var userId = GetUserId();
        var cancelled = await _scanService.CancelScanAsync(id, userId);
        
        if (cancelled)
        {
            _logger.LogInformation($"Scan cancelled: {id} by user {userId}");
            return Ok(new { message = "Scan cancelled successfully" });
        }
        else
        {
            return BadRequest(new { message = "Scan cannot be cancelled in its current state" });
        }
    }

    /// <summary>
    /// Initiate a security scan for a project
    /// </summary>
    [HttpPost("security")]
    public async Task<ActionResult<ScanReportDto>> InitiateSecurityScan([FromBody] int projectId)
    {
        var userId = GetUserId();
        var requestDto = new ScanRequestDto
        {
            Type = Models.ScanType.Security,
            ProjectId = projectId
        };
        
        var scanReport = await _scanService.InitiateScanAsync(userId, requestDto);
        _logger.LogInformation($"Security scan initiated: {scanReport.Id} for project {projectId} by user {userId}");
        return CreatedAtAction(nameof(GetScanReport), new { id = scanReport.Id }, scanReport);
    }

    /// <summary>
    /// Initiate an SEO scan for a project
    /// </summary>
    [HttpPost("seo")]
    public async Task<ActionResult<ScanReportDto>> InitiateSeoScan([FromBody] int projectId)
    {
        var userId = GetUserId();
        var requestDto = new ScanRequestDto
        {
            Type = Models.ScanType.SEO,
            ProjectId = projectId
        };
        
        var scanReport = await _scanService.InitiateScanAsync(userId, requestDto);
        _logger.LogInformation($"SEO scan initiated: {scanReport.Id} for project {projectId} by user {userId}");
        return CreatedAtAction(nameof(GetScanReport), new { id = scanReport.Id }, scanReport);
    }

    /// <summary>
    /// Initiate a performance scan for a project
    /// </summary>
    [HttpPost("performance")]
    public async Task<ActionResult<ScanReportDto>> InitiatePerformanceScan([FromBody] int projectId)
    {
        var userId = GetUserId();
        var requestDto = new ScanRequestDto
        {
            Type = Models.ScanType.Performance,
            ProjectId = projectId
        };
        
        var scanReport = await _scanService.InitiateScanAsync(userId, requestDto);
        _logger.LogInformation($"Performance scan initiated: {scanReport.Id} for project {projectId} by user {userId}");
        return CreatedAtAction(nameof(GetScanReport), new { id = scanReport.Id }, scanReport);
    }

    /// <summary>
    /// Initiate an accessibility scan for a project
    /// </summary>
    [HttpPost("accessibility")]
    public async Task<ActionResult<ScanReportDto>> InitiateAccessibilityScan([FromBody] int projectId)
    {
        var userId = GetUserId();
        var requestDto = new ScanRequestDto
        {
            Type = Models.ScanType.Accessibility,
            ProjectId = projectId
        };
        
        var scanReport = await _scanService.InitiateScanAsync(userId, requestDto);
        _logger.LogInformation($"Accessibility scan initiated: {scanReport.Id} for project {projectId} by user {userId}");
        return CreatedAtAction(nameof(GetScanReport), new { id = scanReport.Id }, scanReport);
    }

    private int GetUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (int.TryParse(userIdClaim, out var userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("Invalid user token");
    }
}
