using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Siteshipserver.DTOs;
using Siteshipserver.Services;
using System.Security.Claims;

namespace Siteshipserver.Controllers;

[ApiController]
[Route("api/projects/{projectId}/[controller]")]
[Authorize]
public class FilesController : ControllerBase
{
    private readonly IFileService _fileService;
    private readonly ILogger<FilesController> _logger;

    public FilesController(IFileService fileService, ILogger<FilesController> logger)
    {
        _fileService = fileService;
        _logger = logger;
    }

    /// <summary>
    /// Create a new file in the project
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<ProjectFileDto>> CreateFile(int projectId, [FromBody] CreateFileDto createDto)
    {
        var userId = GetUserId();
        var file = await _fileService.CreateFileAsync(projectId, userId, createDto);
        _logger.LogInformation($"File created: {file.Id} in project {projectId} by user {userId}");
        return CreatedAtAction(nameof(GetFile), new { projectId, id = file.Id }, file);
    }

    /// <summary>
    /// Get file by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<ProjectFileDto>> GetFile(int projectId, int id)
    {
        var userId = GetUserId();
        var file = await _fileService.GetFileByIdAsync(id, userId);
        return Ok(file);
    }

    /// <summary>
    /// Get all files in the project
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<List<ProjectFileDto>>> GetProjectFiles(int projectId)
    {
        var userId = GetUserId();
        var files = await _fileService.GetProjectFilesAsync(projectId, userId);
        return Ok(files);
    }

    /// <summary>
    /// Update file content or metadata
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<ProjectFileDto>> UpdateFile(int projectId, int id, [FromBody] UpdateFileDto updateDto)
    {
        var userId = GetUserId();
        var file = await _fileService.UpdateFileAsync(id, userId, updateDto);
        _logger.LogInformation($"File updated: {id} in project {projectId} by user {userId}");
        return Ok(file);
    }

    /// <summary>
    /// Delete a file
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteFile(int projectId, int id)
    {
        var userId = GetUserId();
        await _fileService.DeleteFileAsync(id, userId);
        _logger.LogInformation($"File deleted: {id} in project {projectId} by user {userId}");
        return NoContent();
    }

    /// <summary>
    /// Upload a file to the project
    /// </summary>
    [HttpPost("upload")]
    public async Task<ActionResult<ProjectFileDto>> UploadFile(int projectId, [FromForm] FileUploadDto uploadDto)
    {
        var userId = GetUserId();
        var file = await _fileService.UploadFileAsync(projectId, userId, uploadDto);
        _logger.LogInformation($"File uploaded: {file.Id} in project {projectId} by user {userId}");
        return CreatedAtAction(nameof(GetFile), new { projectId, id = file.Id }, file);
    }

    /// <summary>
    /// Download a file
    /// </summary>
    [HttpGet("{id}/download")]
    public async Task<ActionResult> DownloadFile(int projectId, int id)
    {
        var userId = GetUserId();
        var fileContent = await _fileService.DownloadFileAsync(id, userId);
        var file = await _fileService.GetFileByIdAsync(id, userId);
        
        return File(fileContent, file.MimeType ?? "application/octet-stream", file.FileName);
    }

    /// <summary>
    /// Perform bulk operations on multiple files
    /// </summary>
    [HttpPost("bulk")]
    public async Task<ActionResult> BulkFileOperation(int projectId, [FromBody] BulkFileOperationDto operationDto)
    {
        var userId = GetUserId();
        await _fileService.BulkFileOperationAsync(projectId, userId, operationDto);
        _logger.LogInformation($"Bulk operation {operationDto.Operation} performed on {operationDto.FileIds.Count} files in project {projectId} by user {userId}");
        return Ok(new { message = $"Bulk operation {operationDto.Operation} completed successfully" });
    }

    /// <summary>
    /// Sync project files to the container
    /// </summary>
    [HttpPost("sync")]
    public async Task<ActionResult> SyncFilesToContainer(int projectId)
    {
        var userId = GetUserId();
        var success = await _fileService.SyncFilesToContainerAsync(projectId, userId);
        
        if (success)
        {
            _logger.LogInformation($"Files synced to container for project {projectId} by user {userId}");
            return Ok(new { message = "Files synced to container successfully" });
        }
        else
        {
            return BadRequest(new { message = "Failed to sync files to container" });
        }
    }

    private int GetUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (int.TryParse(userIdClaim, out var userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("Invalid user token");
    }
}
