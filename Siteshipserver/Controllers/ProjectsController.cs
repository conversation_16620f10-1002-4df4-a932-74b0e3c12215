using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Siteshipserver.DTOs;
using Siteshipserver.Services;
using System.Security.Claims;

namespace Siteshipserver.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class ProjectsController : ControllerBase
{
    private readonly IProjectService _projectService;
    private readonly ILogger<ProjectsController> _logger;

    public ProjectsController(IProjectService projectService, ILogger<ProjectsController> logger)
    {
        _projectService = projectService;
        _logger = logger;
    }

    /// <summary>
    /// Create a new project
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<ProjectResponseDto>> CreateProject([FromBody] CreateProjectDto createDto)
    {
        var userId = GetUserId();
        var project = await _projectService.CreateProjectAsync(userId, createDto);
        _logger.LogInformation($"Project created: {project.Id} by user {userId}");
        return CreatedAtAction(nameof(GetProject), new { id = project.Id }, project);
    }

    /// <summary>
    /// Get project by ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<ProjectResponseDto>> GetProject(int id)
    {
        var userId = GetUserId();
        var project = await _projectService.GetProjectByIdAsync(id, userId);
        return Ok(project);
    }

    /// <summary>
    /// Get detailed project information including files and scan reports
    /// </summary>
    [HttpGet("{id}/details")]
    public async Task<ActionResult<ProjectDetailDto>> GetProjectDetails(int id)
    {
        var userId = GetUserId();
        var project = await _projectService.GetProjectDetailAsync(id, userId);
        return Ok(project);
    }

    /// <summary>
    /// Get all projects for the current user
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<List<ProjectResponseDto>>> GetUserProjects()
    {
        var userId = GetUserId();
        var projects = await _projectService.GetUserProjectsAsync(userId);
        return Ok(projects);
    }

    /// <summary>
    /// Update project information
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<ProjectResponseDto>> UpdateProject(int id, [FromBody] UpdateProjectDto updateDto)
    {
        var userId = GetUserId();
        var project = await _projectService.UpdateProjectAsync(id, userId, updateDto);
        _logger.LogInformation($"Project updated: {id} by user {userId}");
        return Ok(project);
    }

    /// <summary>
    /// Delete a project
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteProject(int id)
    {
        var userId = GetUserId();
        await _projectService.DeleteProjectAsync(id, userId);
        _logger.LogInformation($"Project deleted: {id} by user {userId}");
        return NoContent();
    }

    /// <summary>
    /// Perform container actions (start, stop, restart, deploy)
    /// </summary>
    [HttpPost("{id}/container")]
    public async Task<ActionResult<ProjectResponseDto>> ContainerAction(int id, [FromBody] ContainerActionDto actionDto)
    {
        var userId = GetUserId();
        var project = await _projectService.PerformContainerActionAsync(id, userId, actionDto);
        _logger.LogInformation($"Container action {actionDto.Action} performed on project {id} by user {userId}");
        return Ok(project);
    }

    /// <summary>
    /// Get public projects (no authentication required)
    /// </summary>
    [HttpGet("public")]
    [AllowAnonymous]
    public async Task<ActionResult<List<ProjectResponseDto>>> GetPublicProjects([FromQuery] int page = 1, [FromQuery] int pageSize = 20)
    {
        if (pageSize > 100) pageSize = 100; // Limit page size
        var projects = await _projectService.GetPublicProjectsAsync(page, pageSize);
        return Ok(projects);
    }

    private int GetUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (int.TryParse(userIdClaim, out var userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("Invalid user token");
    }
}
