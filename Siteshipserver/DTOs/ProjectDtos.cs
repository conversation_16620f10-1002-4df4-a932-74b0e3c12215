using System.ComponentModel.DataAnnotations;
using Siteshipserver.Models;

namespace Siteshipserver.DTOs;

public class CreateProjectDto
{
    [Required]
    [StringLength(200, MinimumLength = 1)]
    public string Name { get; set; } = string.Empty;
    
    [StringLength(1000)]
    public string? Description { get; set; }
    
    [Required]
    public ProjectType Type { get; set; }
    
    public bool IsPublic { get; set; } = false;
}

public class UpdateProjectDto
{
    [StringLength(200, MinimumLength = 1)]
    public string? Name { get; set; }
    
    [StringLength(1000)]
    public string? Description { get; set; }
    
    public bool? IsPublic { get; set; }
}

public class ProjectResponseDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public ProjectType Type { get; set; }
    public ContainerStatus ContainerStatus { get; set; }
    public string? ContainerId { get; set; }
    public string? ContainerPort { get; set; }
    public bool IsPublic { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public DateTime? LastDeployedAt { get; set; }
    public int UserId { get; set; }
    public string? ContainerUrl { get; set; }
}

public class ProjectDetailDto : ProjectResponseDto
{
    public List<ProjectFileDto> Files { get; set; } = new();
    public List<ScanReportDto> ScanReports { get; set; } = new();
}

public class ContainerActionDto
{
    [Required]
    public ContainerAction Action { get; set; }
}

public enum ContainerAction
{
    Start,
    Stop,
    Restart,
    Deploy
}
