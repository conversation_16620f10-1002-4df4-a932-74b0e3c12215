using System.ComponentModel.DataAnnotations;
using Siteshipserver.Models;

namespace Siteshipserver.DTOs;

public class ScanRequestDto
{
    [Required]
    public ScanType Type { get; set; }
    
    [Required]
    public int ProjectId { get; set; }
}

public class ScanReportDto
{
    public int Id { get; set; }
    public ScanType Type { get; set; }
    public ScanStatus Status { get; set; }
    public string Results { get; set; } = string.Empty;
    public int Score { get; set; }
    public string? Summary { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public string? ErrorMessage { get; set; }
    public int ProjectId { get; set; }
    public int UserId { get; set; }
}

public class ScanResultsDto
{
    public List<ScanReportDto> SecurityScans { get; set; } = new();
    public List<ScanReportDto> SeoScans { get; set; } = new();
    public List<ScanReportDto> PerformanceScans { get; set; } = new();
    public List<ScanReportDto> AccessibilityScans { get; set; } = new();
    public ScanSummaryDto Summary { get; set; } = new();
}

public class ScanSummaryDto
{
    public int TotalScans { get; set; }
    public int CompletedScans { get; set; }
    public int FailedScans { get; set; }
    public int PendingScans { get; set; }
    public double AverageScore { get; set; }
    public DateTime? LastScanDate { get; set; }
}
