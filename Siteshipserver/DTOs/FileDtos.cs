using System.ComponentModel.DataAnnotations;

namespace Siteshipserver.DTOs;

public class CreateFileDto
{
    [Required]
    [StringLength(500)]
    public string FilePath { get; set; } = string.Empty;
    
    [Required]
    [StringLength(255)]
    public string FileName { get; set; } = string.Empty;
    
    public string Content { get; set; } = string.Empty;
}

public class UpdateFileDto
{
    public string? Content { get; set; }
    
    [StringLength(500)]
    public string? FilePath { get; set; }
    
    [StringLength(255)]
    public string? FileName { get; set; }
}

public class ProjectFileDto
{
    public int Id { get; set; }
    public string FilePath { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string? MimeType { get; set; }
    public long FileSize { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public int ProjectId { get; set; }
}

public class FileUploadDto
{
    [Required]
    public IFormFile File { get; set; } = null!;
    
    [Required]
    [StringLength(500)]
    public string FilePath { get; set; } = string.Empty;
}

public class BulkFileOperationDto
{
    [Required]
    public List<int> FileIds { get; set; } = new();
    
    [Required]
    public FileOperation Operation { get; set; }
}

public enum FileOperation
{
    Delete,
    Archive,
    Restore
}
