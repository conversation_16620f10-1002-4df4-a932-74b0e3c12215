using System.ComponentModel.DataAnnotations;

namespace Siteshipserver.Models;

public class ProjectFile
{
    public int Id { get; set; }
    
    [Required]
    [StringLength(500)]
    public string FilePath { get; set; } = string.Empty;
    
    [Required]
    [StringLength(255)]
    public string FileName { get; set; } = string.Empty;
    
    public string Content { get; set; } = string.Empty;
    
    [StringLength(100)]
    public string? MimeType { get; set; }
    
    public long FileSize { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    public bool IsDeleted { get; set; } = false;
    
    // Foreign keys
    public int ProjectId { get; set; }
    
    // Navigation properties
    public virtual Project Project { get; set; } = null!;
}
