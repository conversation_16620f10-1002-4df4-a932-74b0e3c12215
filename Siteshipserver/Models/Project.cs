using System.ComponentModel.DataAnnotations;

namespace Siteshipserver.Models;

public class Project
{
    public int Id { get; set; }
    
    [Required]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;
    
    [StringLength(1000)]
    public string? Description { get; set; }
    
    public ProjectType Type { get; set; }
    
    public ContainerStatus ContainerStatus { get; set; } = ContainerStatus.Stopped;
    
    [StringLength(100)]
    public string? ContainerId { get; set; }
    
    [StringLength(50)]
    public string? ContainerPort { get; set; }
    
    public bool IsPublic { get; set; } = false;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? LastDeployedAt { get; set; }
    
    // Foreign keys
    public int UserId { get; set; }
    
    // Navigation properties
    public virtual User User { get; set; } = null!;
    public virtual ICollection<ProjectFile> Files { get; set; } = new List<ProjectFile>();
    public virtual ICollection<ScanReport> ScanReports { get; set; } = new List<ScanReport>();
}

public enum ProjectType
{
    Static = 0,
    React = 1,
    NodeJs = 2,
    Python = 3,
    Vue = 4,
    Angular = 5
}

public enum ContainerStatus
{
    Stopped = 0,
    Starting = 1,
    Running = 2,
    Stopping = 3,
    Error = 4
}
