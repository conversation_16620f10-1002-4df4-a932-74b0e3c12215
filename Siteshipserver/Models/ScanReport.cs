using System.ComponentModel.DataAnnotations;

namespace Siteshipserver.Models;

public class ScanReport
{
    public int Id { get; set; }
    
    public ScanType Type { get; set; }
    
    public ScanStatus Status { get; set; } = ScanStatus.Pending;
    
    public string Results { get; set; } = string.Empty; // JSON string
    
    public int Score { get; set; } = 0; // 0-100 score
    
    [StringLength(1000)]
    public string? Summary { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? CompletedAt { get; set; }
    
    [StringLength(1000)]
    public string? ErrorMessage { get; set; }
    
    // Foreign keys
    public int ProjectId { get; set; }
    public int UserId { get; set; }
    
    // Navigation properties
    public virtual Project Project { get; set; } = null!;
    public virtual User User { get; set; } = null!;
}

public enum ScanType
{
    Security = 0,
    SEO = 1,
    Performance = 2,
    Accessibility = 3
}

public enum ScanStatus
{
    Pending = 0,
    Running = 1,
    Completed = 2,
    Failed = 3,
    Cancelled = 4
}
