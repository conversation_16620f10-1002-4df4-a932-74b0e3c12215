{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=siteship;Username=postgres;Password=your_password_here", "Docker": "unix:///var/run/docker.sock"}, "JwtSettings": {"SecretKey": "your-super-secret-jwt-key-that-should-be-at-least-32-characters-long", "Issuer": "SiteShip.ai", "Audience": "SiteShip.ai-users", "ExpirationHours": 1}, "Docker": {"BaseUrl": "http://localhost"}, "Hangfire": {"DashboardPath": "/hangfire", "DashboardTitle": "SiteShip Background Jobs"}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:5173", "https://localhost:3000", "https://localhost:5173"]}}